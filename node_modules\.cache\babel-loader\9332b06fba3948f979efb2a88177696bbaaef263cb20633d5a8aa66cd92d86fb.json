{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import axios from'axios';import{FiSend,FiUpload}from'react-icons/fi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Chatbot=()=>{const[messages,setMessages]=useState([]);const[input,setInput]=useState('');const[isLoading,setIsLoading]=useState(false);const[file,setFile]=useState(null);const messagesEndRef=useRef(null);const scrollToBottom=()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});};useEffect(()=>{scrollToBottom();},[messages]);const handleSend=async()=>{if(!input.trim())return;const userMessage={text:input,sender:'user'};setMessages(prev=>[...prev,userMessage]);setInput('');setIsLoading(true);try{const response=await axios.post('http://localhost:5000/api/chat',{message:input,history:messages.map(msg=>({role:msg.sender==='user'?'user':'assistant',content:msg.text}))});const botMessage={text:response.data.reply,sender:'bot'};setMessages(prev=>[...prev,botMessage]);}catch(error){console.error('Error:',error);setMessages(prev=>[...prev,{text:'Sorry, I encountered an error. Please try again.',sender:'bot'}]);}finally{setIsLoading(false);}};const handleFileUpload=async e=>{const file=e.target.files[0];if(!file)return;setFile(file);const formData=new FormData();formData.append('file',file);try{await axios.post('http://localhost:5000/api/upload',formData,{headers:{'Content-Type':'multipart/form-data'}});alert('Document uploaded successfully!');}catch(error){console.error('Upload error:',error);alert('Failed to upload document');}};return/*#__PURE__*/_jsxs(\"div\",{className:\"chatbot-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"chatbot-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"DeepSeek Chatbot\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-upload\",children:[/*#__PURE__*/_jsxs(\"label\",{htmlFor:\"file-upload\",className:\"upload-button\",children:[/*#__PURE__*/_jsx(FiUpload,{}),\" Upload Document\"]}),/*#__PURE__*/_jsx(\"input\",{id:\"file-upload\",type:\"file\",accept:\".pdf,.txt,.docx\",onChange:handleFileUpload,style:{display:'none'}}),file&&/*#__PURE__*/_jsx(\"span\",{className:\"file-name\",children:file.name})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"chat-messages\",children:[messages.map((message,index)=>/*#__PURE__*/_jsx(\"div\",{className:`message ${message.sender}`,children:message.text},index)),isLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"message bot\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"typing-indicator\",children:[/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"span\",{}),/*#__PURE__*/_jsx(\"span\",{})]})}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"chat-input\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:input,onChange:e=>setInput(e.target.value),onKeyPress:e=>e.key==='Enter'&&handleSend(),placeholder:\"Type your message...\",disabled:isLoading}),/*#__PURE__*/_jsx(\"button\",{onClick:handleSend,disabled:isLoading||!input.trim(),children:/*#__PURE__*/_jsx(FiSend,{})})]})]});};export default Chatbot;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "axios", "FiSend", "FiUpload", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON>", "messages", "setMessages", "input", "setInput", "isLoading", "setIsLoading", "file", "setFile", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSend", "trim", "userMessage", "text", "sender", "prev", "response", "post", "message", "history", "map", "msg", "role", "content", "botMessage", "data", "reply", "error", "console", "handleFileUpload", "e", "target", "files", "formData", "FormData", "append", "headers", "alert", "className", "children", "htmlFor", "id", "type", "accept", "onChange", "style", "display", "name", "index", "ref", "value", "onKeyPress", "key", "placeholder", "disabled", "onClick"], "sources": ["E:/HelpLive/fronend/src/Chatbot.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { FiSend, FiUpload } from 'react-icons/fi';\r\n\r\nconst Chatbot = () => {\r\n  const [messages, setMessages] = useState([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [file, setFile] = useState(null);\r\n  const messagesEndRef = useRef(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const handleSend = async () => {\r\n    if (!input.trim()) return;\r\n\r\n    const userMessage = { text: input, sender: 'user' };\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await axios.post('http://localhost:5000/api/chat', {\r\n        message: input,\r\n        history: messages.map(msg => ({\r\n          role: msg.sender === 'user' ? 'user' : 'assistant',\r\n          content: msg.text\r\n        }))\r\n      });\r\n\r\n      const botMessage = { text: response.data.reply, sender: 'bot' };\r\n      setMessages(prev => [...prev, botMessage]);\r\n    } catch (error) {\r\n      console.error('Error:', error);\r\n      setMessages(prev => [...prev, { \r\n        text: 'Sorry, I encountered an error. Please try again.', \r\n        sender: 'bot' \r\n      }]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    setFile(file);\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    try {\r\n      await axios.post('http://localhost:5000/api/upload', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      alert('Document uploaded successfully!');\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      alert('Failed to upload document');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chatbot-container\">\r\n      <div className=\"chatbot-header\">\r\n        <h2>DeepSeek Chatbot</h2>\r\n        <div className=\"file-upload\">\r\n          <label htmlFor=\"file-upload\" className=\"upload-button\">\r\n            <FiUpload /> Upload Document\r\n          </label>\r\n          <input \r\n            id=\"file-upload\" \r\n            type=\"file\" \r\n            accept=\".pdf,.txt,.docx\" \r\n            onChange={handleFileUpload}\r\n            style={{ display: 'none' }}\r\n          />\r\n          {file && <span className=\"file-name\">{file.name}</span>}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"chat-messages\">\r\n        {messages.map((message, index) => (\r\n          <div key={index} className={`message ${message.sender}`}>\r\n            {message.text}\r\n          </div>\r\n        ))}\r\n        {isLoading && (\r\n          <div className=\"message bot\">\r\n            <div className=\"typing-indicator\">\r\n              <span></span>\r\n              <span></span>\r\n              <span></span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      <div className=\"chat-input\">\r\n        <input\r\n          type=\"text\"\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyPress={(e) => e.key === 'Enter' && handleSend()}\r\n          placeholder=\"Type your message...\"\r\n          disabled={isLoading}\r\n        />\r\n        <button onClick={handleSend} disabled={isLoading || !input.trim()}>\r\n          <FiSend />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Chatbot;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,QAAQ,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CACpB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGZ,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACa,KAAK,CAAEC,QAAQ,CAAC,CAAGd,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACe,SAAS,CAAEC,YAAY,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACiB,IAAI,CAAEC,OAAO,CAAC,CAAGlB,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAAAmB,cAAc,CAAGlB,MAAM,CAAC,IAAI,CAAC,CAEnC,KAAM,CAAAmB,cAAc,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAC3B,CAAAA,qBAAA,CAAAF,cAAc,CAACG,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAEDtB,SAAS,CAAC,IAAM,CACdkB,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAACT,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAc,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAACZ,KAAK,CAACa,IAAI,CAAC,CAAC,CAAE,OAEnB,KAAM,CAAAC,WAAW,CAAG,CAAEC,IAAI,CAAEf,KAAK,CAAEgB,MAAM,CAAE,MAAO,CAAC,CACnDjB,WAAW,CAACkB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEH,WAAW,CAAC,CAAC,CAC3Cb,QAAQ,CAAC,EAAE,CAAC,CACZE,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAA5B,KAAK,CAAC6B,IAAI,CAAC,gCAAgC,CAAE,CAClEC,OAAO,CAAEpB,KAAK,CACdqB,OAAO,CAAEvB,QAAQ,CAACwB,GAAG,CAACC,GAAG,GAAK,CAC5BC,IAAI,CAAED,GAAG,CAACP,MAAM,GAAK,MAAM,CAAG,MAAM,CAAG,WAAW,CAClDS,OAAO,CAAEF,GAAG,CAACR,IACf,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,KAAM,CAAAW,UAAU,CAAG,CAAEX,IAAI,CAAEG,QAAQ,CAACS,IAAI,CAACC,KAAK,CAAEZ,MAAM,CAAE,KAAM,CAAC,CAC/DjB,WAAW,CAACkB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAES,UAAU,CAAC,CAAC,CAC5C,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,CAAEA,KAAK,CAAC,CAC9B9B,WAAW,CAACkB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,CAC5BF,IAAI,CAAE,kDAAkD,CACxDC,MAAM,CAAE,KACV,CAAC,CAAC,CAAC,CACL,CAAC,OAAS,CACRb,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAA4B,gBAAgB,CAAG,KAAO,CAAAC,CAAC,EAAK,CACpC,KAAM,CAAA5B,IAAI,CAAG4B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAI,CAAC9B,IAAI,CAAE,OAEXC,OAAO,CAACD,IAAI,CAAC,CACb,KAAM,CAAA+B,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEjC,IAAI,CAAC,CAE7B,GAAI,CACF,KAAM,CAAAd,KAAK,CAAC6B,IAAI,CAAC,kCAAkC,CAAEgB,QAAQ,CAAE,CAC7DG,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACFC,KAAK,CAAC,iCAAiC,CAAC,CAC1C,CAAE,MAAOV,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCU,KAAK,CAAC,2BAA2B,CAAC,CACpC,CACF,CAAC,CAED,mBACE3C,KAAA,QAAK4C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,KAAA,QAAK4C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/C,IAAA,OAAA+C,QAAA,CAAI,kBAAgB,CAAI,CAAC,cACzB7C,KAAA,QAAK4C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7C,KAAA,UAAO8C,OAAO,CAAC,aAAa,CAACF,SAAS,CAAC,eAAe,CAAAC,QAAA,eACpD/C,IAAA,CAACF,QAAQ,GAAE,CAAC,mBACd,EAAO,CAAC,cACRE,IAAA,UACEiD,EAAE,CAAC,aAAa,CAChBC,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,iBAAiB,CACxBC,QAAQ,CAAEf,gBAAiB,CAC3BgB,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,CACD5C,IAAI,eAAIV,IAAA,SAAM8C,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAErC,IAAI,CAAC6C,IAAI,CAAO,CAAC,EACpD,CAAC,EACH,CAAC,cAENrD,KAAA,QAAK4C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3B3C,QAAQ,CAACwB,GAAG,CAAC,CAACF,OAAO,CAAE8B,KAAK,gBAC3BxD,IAAA,QAAiB8C,SAAS,CAAE,WAAWpB,OAAO,CAACJ,MAAM,EAAG,CAAAyB,QAAA,CACrDrB,OAAO,CAACL,IAAI,EADLmC,KAEL,CACN,CAAC,CACDhD,SAAS,eACRR,IAAA,QAAK8C,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B7C,KAAA,QAAK4C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B/C,IAAA,UAAY,CAAC,cACbA,IAAA,UAAY,CAAC,cACbA,IAAA,UAAY,CAAC,EACV,CAAC,CACH,CACN,cACDA,IAAA,QAAKyD,GAAG,CAAE7C,cAAe,CAAE,CAAC,EACzB,CAAC,cAENV,KAAA,QAAK4C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB/C,IAAA,UACEkD,IAAI,CAAC,MAAM,CACXQ,KAAK,CAAEpD,KAAM,CACb8C,QAAQ,CAAGd,CAAC,EAAK/B,QAAQ,CAAC+B,CAAC,CAACC,MAAM,CAACmB,KAAK,CAAE,CAC1CC,UAAU,CAAGrB,CAAC,EAAKA,CAAC,CAACsB,GAAG,GAAK,OAAO,EAAI1C,UAAU,CAAC,CAAE,CACrD2C,WAAW,CAAC,sBAAsB,CAClCC,QAAQ,CAAEtD,SAAU,CACrB,CAAC,cACFR,IAAA,WAAQ+D,OAAO,CAAE7C,UAAW,CAAC4C,QAAQ,CAAEtD,SAAS,EAAI,CAACF,KAAK,CAACa,IAAI,CAAC,CAAE,CAAA4B,QAAA,cAChE/C,IAAA,CAACH,MAAM,GAAE,CAAC,CACJ,CAAC,EACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAM,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}