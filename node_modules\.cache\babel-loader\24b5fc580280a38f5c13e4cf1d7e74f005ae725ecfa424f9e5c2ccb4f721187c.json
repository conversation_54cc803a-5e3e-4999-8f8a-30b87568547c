{"ast": null, "code": "var _jsxFileName = \"E:\\\\HelpLive\\\\fronend\\\\src\\\\Chatbot.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport axios from 'axios';\nimport { FiSend, FiUpload } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Chatbot = () => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [file, setFile] = useState(null);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSend = async () => {\n    if (!input.trim()) return;\n    const userMessage = {\n      text: input,\n      sender: 'user'\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInput('');\n    setIsLoading(true);\n    try {\n      const response = await axios.post('http://localhost:5000/api/chat', {\n        message: input,\n        history: messages.map(msg => ({\n          role: msg.sender === 'user' ? 'user' : 'assistant',\n          content: msg.text\n        }))\n      });\n      const botMessage = {\n        text: response.data.reply,\n        sender: 'bot'\n      };\n      setMessages(prev => [...prev, botMessage]);\n    } catch (error) {\n      console.error('Error:', error);\n      setMessages(prev => [...prev, {\n        text: 'Sorry, I encountered an error. Please try again.',\n        sender: 'bot'\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleFileUpload = async e => {\n    const file = e.target.files[0];\n    if (!file) return;\n    setFile(file);\n    const formData = new FormData();\n    formData.append('file', file);\n    try {\n      await axios.post('http://localhost:5000/api/upload', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      alert('Document uploaded successfully!');\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Failed to upload document');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chatbot-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chatbot-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"DeepSeek Chatbot\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"file-upload\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-upload\",\n          className: \"upload-button\",\n          children: [/*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), \" Upload Document\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-upload\",\n          type: \"file\",\n          accept: \".pdf,.txt,.docx\",\n          onChange: handleFileUpload,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), file && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"file-name\",\n          children: file.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 20\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-messages\",\n      children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `message ${message.sender}`,\n        children: message.text\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message bot\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"typing-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-input\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: input,\n        onChange: e => setInput(e.target.value),\n        onKeyPress: e => e.key === 'Enter' && handleSend(),\n        placeholder: \"Type your message...\",\n        disabled: isLoading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSend,\n        disabled: isLoading || !input.trim(),\n        children: /*#__PURE__*/_jsxDEV(FiSend, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(Chatbot, \"suFBwRv2+d/zeDJqske0OKhjws0=\");\n_c = Chatbot;\nexport default Chatbot;\nvar _c;\n$RefreshReg$(_c, \"Chatbot\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "axios", "FiSend", "FiUpload", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "messages", "setMessages", "input", "setInput", "isLoading", "setIsLoading", "file", "setFile", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSend", "trim", "userMessage", "text", "sender", "prev", "response", "post", "message", "history", "map", "msg", "role", "content", "botMessage", "data", "reply", "error", "console", "handleFileUpload", "e", "target", "files", "formData", "FormData", "append", "headers", "alert", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "id", "type", "accept", "onChange", "style", "display", "name", "index", "ref", "value", "onKeyPress", "key", "placeholder", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["E:/HelpLive/fronend/src/Chatbot.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { FiSend, FiUpload } from 'react-icons/fi';\r\n\r\nconst Chatbot = () => {\r\n  const [messages, setMessages] = useState([]);\r\n  const [input, setInput] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [file, setFile] = useState(null);\r\n  const messagesEndRef = useRef(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const handleSend = async () => {\r\n    if (!input.trim()) return;\r\n\r\n    const userMessage = { text: input, sender: 'user' };\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInput('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const response = await axios.post('http://localhost:5000/api/chat', {\r\n        message: input,\r\n        history: messages.map(msg => ({\r\n          role: msg.sender === 'user' ? 'user' : 'assistant',\r\n          content: msg.text\r\n        }))\r\n      });\r\n\r\n      const botMessage = { text: response.data.reply, sender: 'bot' };\r\n      setMessages(prev => [...prev, botMessage]);\r\n    } catch (error) {\r\n      console.error('Error:', error);\r\n      setMessages(prev => [...prev, { \r\n        text: 'Sorry, I encountered an error. Please try again.', \r\n        sender: 'bot' \r\n      }]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    setFile(file);\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    try {\r\n      await axios.post('http://localhost:5000/api/upload', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      alert('Document uploaded successfully!');\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      alert('Failed to upload document');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chatbot-container\">\r\n      <div className=\"chatbot-header\">\r\n        <h2>DeepSeek Chatbot</h2>\r\n        <div className=\"file-upload\">\r\n          <label htmlFor=\"file-upload\" className=\"upload-button\">\r\n            <FiUpload /> Upload Document\r\n          </label>\r\n          <input \r\n            id=\"file-upload\" \r\n            type=\"file\" \r\n            accept=\".pdf,.txt,.docx\" \r\n            onChange={handleFileUpload}\r\n            style={{ display: 'none' }}\r\n          />\r\n          {file && <span className=\"file-name\">{file.name}</span>}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"chat-messages\">\r\n        {messages.map((message, index) => (\r\n          <div key={index} className={`message ${message.sender}`}>\r\n            {message.text}\r\n          </div>\r\n        ))}\r\n        {isLoading && (\r\n          <div className=\"message bot\">\r\n            <div className=\"typing-indicator\">\r\n              <span></span>\r\n              <span></span>\r\n              <span></span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n\r\n      <div className=\"chat-input\">\r\n        <input\r\n          type=\"text\"\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyPress={(e) => e.key === 'Enter' && handleSend()}\r\n          placeholder=\"Type your message...\"\r\n          disabled={isLoading}\r\n        />\r\n        <button onClick={handleSend} disabled={isLoading || !input.trim()}>\r\n          <FiSend />\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Chatbot;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMkB,cAAc,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACdiB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;EAEd,MAAMc,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACZ,KAAK,CAACa,IAAI,CAAC,CAAC,EAAE;IAEnB,MAAMC,WAAW,GAAG;MAAEC,IAAI,EAAEf,KAAK;MAAEgB,MAAM,EAAE;IAAO,CAAC;IACnDjB,WAAW,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,WAAW,CAAC,CAAC;IAC3Cb,QAAQ,CAAC,EAAE,CAAC;IACZE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,IAAI,CAAC,gCAAgC,EAAE;QAClEC,OAAO,EAAEpB,KAAK;QACdqB,OAAO,EAAEvB,QAAQ,CAACwB,GAAG,CAACC,GAAG,KAAK;UAC5BC,IAAI,EAAED,GAAG,CAACP,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,WAAW;UAClDS,OAAO,EAAEF,GAAG,CAACR;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMW,UAAU,GAAG;QAAEX,IAAI,EAAEG,QAAQ,CAACS,IAAI,CAACC,KAAK;QAAEZ,MAAM,EAAE;MAAM,CAAC;MAC/DjB,WAAW,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAES,UAAU,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;MAC9B9B,WAAW,CAACkB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5BF,IAAI,EAAE,kDAAkD;QACxDC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRb,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4B,gBAAgB,GAAG,MAAOC,CAAC,IAAK;IACpC,MAAM5B,IAAI,GAAG4B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI,CAAC9B,IAAI,EAAE;IAEXC,OAAO,CAACD,IAAI,CAAC;IACb,MAAM+B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEjC,IAAI,CAAC;IAE7B,IAAI;MACF,MAAMb,KAAK,CAAC4B,IAAI,CAAC,kCAAkC,EAAEgB,QAAQ,EAAE;QAC7DG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACFC,KAAK,CAAC,iCAAiC,CAAC;IAC1C,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCU,KAAK,CAAC,2BAA2B,CAAC;IACpC;EACF,CAAC;EAED,oBACE5C,OAAA;IAAK6C,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChC9C,OAAA;MAAK6C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B9C,OAAA;QAAA8C,QAAA,EAAI;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzBlD,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA;UAAOmD,OAAO,EAAC,aAAa;UAACN,SAAS,EAAC,eAAe;UAAAC,QAAA,gBACpD9C,OAAA,CAACF,QAAQ;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBACd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRlD,OAAA;UACEoD,EAAE,EAAC,aAAa;UAChBC,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,iBAAiB;UACxBC,QAAQ,EAAEnB,gBAAiB;UAC3BoB,KAAK,EAAE;YAAEC,OAAO,EAAE;UAAO;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,EACDzC,IAAI,iBAAIT,OAAA;UAAM6C,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAErC,IAAI,CAACiD;QAAI;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlD,OAAA;MAAK6C,SAAS,EAAC,eAAe;MAAAC,QAAA,GAC3B3C,QAAQ,CAACwB,GAAG,CAAC,CAACF,OAAO,EAAEkC,KAAK,kBAC3B3D,OAAA;QAAiB6C,SAAS,EAAE,WAAWpB,OAAO,CAACJ,MAAM,EAAG;QAAAyB,QAAA,EACrDrB,OAAO,CAACL;MAAI,GADLuC,KAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACN,CAAC,EACD3C,SAAS,iBACRP,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B9C,OAAA;UAAK6C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B9C,OAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblD,OAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblD,OAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACDlD,OAAA;QAAK4D,GAAG,EAAEjD;MAAe;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENlD,OAAA;MAAK6C,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB9C,OAAA;QACEqD,IAAI,EAAC,MAAM;QACXQ,KAAK,EAAExD,KAAM;QACbkD,QAAQ,EAAGlB,CAAC,IAAK/B,QAAQ,CAAC+B,CAAC,CAACC,MAAM,CAACuB,KAAK,CAAE;QAC1CC,UAAU,EAAGzB,CAAC,IAAKA,CAAC,CAAC0B,GAAG,KAAK,OAAO,IAAI9C,UAAU,CAAC,CAAE;QACrD+C,WAAW,EAAC,sBAAsB;QAClCC,QAAQ,EAAE1D;MAAU;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACFlD,OAAA;QAAQkE,OAAO,EAAEjD,UAAW;QAACgD,QAAQ,EAAE1D,SAAS,IAAI,CAACF,KAAK,CAACa,IAAI,CAAC,CAAE;QAAA4B,QAAA,eAChE9C,OAAA,CAACH,MAAM;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CAtHID,OAAO;AAAAkE,EAAA,GAAPlE,OAAO;AAwHb,eAAeA,OAAO;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}