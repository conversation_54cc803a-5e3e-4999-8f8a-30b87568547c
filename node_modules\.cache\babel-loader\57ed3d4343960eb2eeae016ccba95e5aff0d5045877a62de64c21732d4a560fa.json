{"ast": null, "code": "'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' || typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function') {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.min.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}", "map": {"version": 3, "names": ["checkDCE", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "process", "env", "NODE_ENV", "Error", "err", "console", "error", "module", "exports", "require"], "sources": ["E:/HelpLive/fronend/node_modules/react-dom/index.js"], "sourcesContent": ["'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.min.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQA,CAAA,EAAG;EAClB;EACA,IACE,OAAOC,8BAA8B,KAAK,WAAW,IACrD,OAAOA,8BAA8B,CAACD,QAAQ,KAAK,UAAU,EAC7D;IACA;EACF;EACA,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM,IAAIC,KAAK,CAAC,KAAK,CAAC;EACxB;EACA,IAAI;IACF;IACAJ,8BAA8B,CAACD,QAAQ,CAACA,QAAQ,CAAC;EACnD,CAAC,CAAC,OAAOM,GAAG,EAAE;IACZ;IACA;IACAC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;EACpB;AACF;AAEA,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACA;EACAJ,QAAQ,CAAC,CAAC;EACVS,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAC/D,CAAC,MAAM;EACLF,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}