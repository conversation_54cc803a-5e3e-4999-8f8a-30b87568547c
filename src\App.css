.chatbot-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 600px;
  height: 80vh;
  margin: 0 auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chatbot-header {
  padding: 15px;
  background-color: #4a6fa5;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-upload {
  display: flex;
  align-items: center;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  background-color: #3a5a80;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.upload-button:hover {
  background-color: #2c4361;
}

.file-name {
  margin-left: 10px;
  font-size: 12px;
  color: #eee;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.message {
  margin-bottom: 15px;
  padding: 10px 15px;
  border-radius: 18px;
  max-width: 80%;
  line-height: 1.4;
}

.message.user {
  background-color: #4a6fa5;
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 5px;
}

.message.bot {
  background-color: #e5e5ea;
  color: #000;
  margin-right: auto;
  border-bottom-left-radius: 5px;
}

.typing-indicator {
  display: flex;
  padding: 10px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background-color: #666;
  border-radius: 50%;
  display: inline-block;
  animation: typing 1s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.chat-input {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ddd;
  background-color: white;
}

.chat-input input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
}

.chat-input button {
  margin-left: 10px;
  padding: 10px 15px;
  background-color: #4a6fa5;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.chat-input button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}