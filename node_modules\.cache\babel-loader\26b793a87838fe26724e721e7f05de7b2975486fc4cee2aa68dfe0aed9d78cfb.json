{"ast": null, "code": "import React from'react';import'./App.css';import Chatbot from'./Chatbot';import{jsx as _jsx}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsx(Chatbot,{})});}export default App;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "jsx", "_jsx", "App", "className", "children"], "sources": ["E:/HelpLive/fronend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport './App.css';\nimport Chatbot from './Chatbot';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <Chatbot />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,WAAW,CAClB,MAAO,CAAAC,OAAO,KAAM,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEhC,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACED,IAAA,QAAKE,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBH,IAAA,CAACF,OAAO,GAAE,CAAC,CACR,CAAC,CAEV,CAEA,cAAe,CAAAG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}